"""
金豆发放申请校验系统 - 数据处理和更新模块
负责数据备份、主明细表追加和统计表更新

本模块是数据处理流程的最后一个关键环节，主要功能包括：

核心功能：
1. 数据备份管理：自动备份关键数据表，确保数据安全
2. 主明细表更新：将通过校验的数据追加到主明细表
3. 统计表维护：更新客户统计表和年度统计表
4. 事务处理：确保数据更新的原子性和一致性
5. 临时表管理：处理临时汇总表的清空和重置

处理流程：
临时表处理 -> 数据备份 -> 主表追加 -> 统计表更新 -> 结果报告

数据表操作：
1. 金豆发放申请明细临时汇总表：清空并重置
2. 金豆发放申请主明细表：追加新记录
3. 金豆发放户统计表：更新客户累计统计
4. 金豆发放年统计表：更新年度统计

设计特点：
- 事务安全：确保数据更新的原子性
- 备份机制：处理前自动备份重要数据
- 增量更新：高效的增量统计更新算法
- 错误恢复：支持数据回滚和恢复
- 空值处理：统一处理各种空值情况

安全机制：
- 自动备份：处理前备份所有相关表
- 原子操作：确保所有更新要么全部成功要么全部失败
- 数据验证：更新前验证数据完整性
- 日志记录：详细记录所有操作过程

"""

# 导入标准库
import pandas as pd  # 数据处理核心库
import numpy as np   # 数值计算库，用于空值处理
from typing import Dict, List, Tuple, Any, Optional  # 类型注解
from pathlib import Path  # 路径操作
import logging      # 日志记录
from datetime import datetime  # 日期时间处理
import shutil       # 文件操作
from collections import defaultdict  # 默认字典

# 导入自定义工具类
from utils import ConfigManager, LoggerSetup, PerformanceMonitor, FileManager


class ProcessingResult:
    """处理结果记录"""
    
    def __init__(self):
        """初始化处理结果"""
        self.success = False
        self.processed_records = 0
        self.new_customers = 0
        self.updated_customers = 0
        self.backup_files = []
        self.error_messages = []
        self.warnings = []
        self.start_time = datetime.now()
        self.end_time = None
        self.processing_time = 0
    
    def mark_completed(self, success: bool = True) -> None:
        """标记处理完成"""
        self.success = success
        self.end_time = datetime.now()
        self.processing_time = (self.end_time - self.start_time).total_seconds()
    
    def add_error(self, error_message: str) -> None:
        """添加错误信息"""
        self.error_messages.append(error_message)
    
    def add_warning(self, warning_message: str) -> None:
        """添加警告信息"""
        self.warnings.append(warning_message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            '处理状态': '成功' if self.success else '失败',
            '处理记录数': self.processed_records,
            '新增客户数': self.new_customers,
            '更新客户数': self.updated_customers,
            '备份文件': self.backup_files,
            '处理时间(秒)': self.processing_time,
            '错误信息': self.error_messages,
            '警告信息': self.warnings,
            '开始时间': self.start_time.strftime('%Y-%m-%d %H:%M:%S') if self.start_time else None,
            '结束时间': self.end_time.strftime('%Y-%m-%d %H:%M:%S') if self.end_time else None
        }


class DataProcessor:
    """数据处理器"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化数据处理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager
        self.logger = LoggerSetup.setup_logger('DataProcessor', 
                                             config_manager.get('logging', {}))
        self.monitor = PerformanceMonitor(config_manager)
        self.file_manager = FileManager(config_manager)
        self.result = ProcessingResult()
    
    def _process_temp_summary_table(self, temp_df: pd.DataFrame) -> bool:
        """
        处理临时汇总表：备份现有数据并清空
        
        Args:
            temp_df: 新的临时汇总表数据
            
        Returns:
            是否处理成功
        """
        try:
            self.logger.info("开始处理临时汇总表")
            
            # 获取临时汇总表路径
            raw_data_path = self.config.get('data_paths.raw_data')
            temp_summary_file = Path(raw_data_path) / self.config.get('table_files.temp_summary_table')
            
            # 如果临时汇总表存在，先备份
            if temp_summary_file.exists():
                try:
                    # 备份现有临时汇总表
                    backup_path = self.config.get('data_paths.backup_data')
                    backup_file = self.file_manager.backup_file(str(temp_summary_file), backup_path)
                    self.result.backup_files.append(backup_file)
                    self.logger.info(f"临时汇总表备份成功: {backup_file}")
                    
                    # 读取表头
                    with open(temp_summary_file, 'r', encoding='utf-8-sig') as f:
                        header = f.readline().strip()
                    
                    # 清空文件，只保留表头
                    with open(temp_summary_file, 'w', encoding='utf-8-sig') as f:
                        f.write(header + '\n')
                    
                    self.logger.info("临时汇总表已清空，保留表头")
                    
                except Exception as e:
                    self.logger.error(f"处理临时汇总表时出错: {e}")
                    return False
            else:
                # 如果文件不存在，创建新文件
                self.logger.info("临时汇总表不存在，将创建新表")
                temp_summary_file.parent.mkdir(parents=True, exist_ok=True)
                
                # 使用新数据的列作为表头
                temp_df.head(0).to_csv(temp_summary_file, index=False, encoding='utf-8-sig')
            
            return True
            
        except Exception as e:
            self.logger.error(f"处理临时汇总表时发生错误: {e}")
            return False
    
    def process_validated_data(self, temp_df: pd.DataFrame) -> ProcessingResult:
        """
        处理通过校验的数据
        
        Args:
            temp_df: 通过校验的临时汇总表数据
            
        Returns:
            处理结果
        """
        try:
            self.monitor.start_monitoring("数据处理和更新")
            self.logger.info(f"开始处理通过校验的数据，共 {len(temp_df)} 条记录")
            
            # 步骤0: 处理临时汇总表
            if not self._process_temp_summary_table(temp_df):
                self.result.add_error("临时汇总表处理失败")
                self.result.mark_completed(False)
                return self.result
            
            # 步骤1: 数据备份
            if not self._backup_data_tables():
                self.result.add_error("数据备份失败")
                self.result.mark_completed(False)
                return self.result
            
            self.monitor.checkpoint("数据备份完成")
            
            # 步骤2: 追加到主明细表
            if not self._append_to_main_detail_table(temp_df):
                self.result.add_error("主明细表追加失败")
                self.result.mark_completed(False)
                return self.result
            
            self.monitor.checkpoint("主明细表追加完成")
            
            # 步骤3: 更新统计表
            if not self._update_statistics_tables(temp_df):
                self.result.add_error("统计表更新失败")
                self.result.mark_completed(False)
                return self.result
            
            self.monitor.checkpoint("统计表更新完成")
            
            self.result.processed_records = len(temp_df)
            self.result.mark_completed(True)
            
            self.monitor.end_monitoring("数据处理和更新")
            
            self.logger.info(f"数据处理完成: 成功处理 {self.result.processed_records} 条记录")
            return self.result
            
        except Exception as e:
            self.logger.error(f"处理数据时发生错误: {e}")
            self.result.add_error(f"处理过程异常: {str(e)}")
            self.result.mark_completed(False)
            return self.result
    
    def _backup_data_tables(self) -> bool:
        """
        备份数据表
        
        Returns:
            是否备份成功
        """
        try:
            self.logger.info("开始备份数据表")
            
            raw_data_path = self.config.get('data_paths.raw_data')
            backup_path = self.config.get('data_paths.backup_data')
            
            # 需要备份的表
            tables_to_backup = [
                self.config.get('table_files.customer_stats_table'),
                self.config.get('table_files.main_detail_table'),
                self.config.get('table_files.yearly_stats_table')
            ]
            
            for table_name in tables_to_backup:
                source_file = Path(raw_data_path) / table_name
                
                if source_file.exists():
                    try:
                        backup_file = self.file_manager.backup_file(str(source_file), backup_path)
                        self.result.backup_files.append(backup_file)
                        self.logger.info(f"备份成功: {table_name}")
                    except Exception as e:
                        self.logger.warning(f"备份文件 {table_name} 失败: {e}")
                        self.result.add_warning(f"备份文件 {table_name} 失败: {e}")
                else:
                    self.logger.info(f"文件不存在，跳过备份: {table_name}")
            
            self.logger.info(f"数据表备份完成，共备份 {len(self.result.backup_files)} 个文件")
            return True
            
        except Exception as e:
            self.logger.error(f"备份数据表时发生错误: {e}")
            return False
    
    def _append_to_main_detail_table(self, temp_df: pd.DataFrame) -> bool:
        """
        追加数据到主明细表
        
        Args:
            temp_df: 临时汇总表数据
            
        Returns:
            是否追加成功
        """
        try:
            self.logger.info("开始追加数据到主明细表")
            
            raw_data_path = self.config.get('data_paths.raw_data')
            main_table_file = Path(raw_data_path) / self.config.get('table_files.main_detail_table')
            
            # 指定需要保持为字符串类型的列
            dtype_dict = {
                '客户手机号': str,
                '客户名称': str,
                '客户归属企业': str,
                '申请发放机构': str,
                '活动方案全称': str,
                '归属条线': str,
                '备注（非必填）': str
            }
            
            # 读取现有主明细表
            if main_table_file.exists():
                main_df = pd.read_csv(main_table_file, encoding='utf-8-sig', dtype=dtype_dict)
                self.logger.info(f"读取现有主明细表: {len(main_df)} 条记录")
            else:
                main_df = pd.DataFrame()
                self.logger.info("主明细表不存在，将创建新表")
            
            # 准备要追加的数据
            temp_df_to_append = temp_df.copy()
            
            # 生成新的记录ID
            if not main_df.empty and '记录ID' in main_df.columns:
                max_id = main_df['记录ID'].max()
                start_id = max_id + 1
            else:
                start_id = 1
            
            temp_df_to_append['记录ID'] = range(start_id, start_id + len(temp_df_to_append))
            
            # 确保列顺序一致
            if not main_df.empty:
                # 使用主表的列顺序
                columns_order = main_df.columns.tolist()
                # 添加临时表中可能存在的新列
                for col in temp_df_to_append.columns:
                    if col not in columns_order:
                        columns_order.append(col)
                
                temp_df_to_append = temp_df_to_append.reindex(columns=columns_order, fill_value='')
            else:
                # 调整列顺序，将记录ID放在第一列
                columns = ['记录ID'] + [col for col in temp_df_to_append.columns if col != '记录ID']
                temp_df_to_append = temp_df_to_append[columns]
            
            # 合并数据
            if not main_df.empty:
                updated_main_df = pd.concat([main_df, temp_df_to_append], ignore_index=True)
            else:
                updated_main_df = temp_df_to_append
            
            # 保存更新后的主明细表
            updated_main_df.to_csv(main_table_file, index=False, encoding='utf-8-sig')
            
            self.logger.info(f"主明细表追加成功: 新增 {len(temp_df_to_append)} 条记录，"
                           f"总计 {len(updated_main_df)} 条记录")
            
            return True
            
        except Exception as e:
            self.logger.error(f"追加主明细表时发生错误: {e}")
            return False
    
    def _update_statistics_tables(self, temp_df: pd.DataFrame) -> bool:
        """
        更新统计表
        
        Args:
            temp_df: 临时汇总表数据
            
        Returns:
            是否更新成功
        """
        try:
            self.logger.info("开始更新统计表")
            
            # 更新客户统计表
            if not self._update_customer_statistics(temp_df):
                return False
            
            # 更新年度统计表
            if not self._update_yearly_statistics(temp_df):
                return False
            
            self.logger.info("统计表更新完成")
            return True
            
        except Exception as e:
            self.logger.error(f"更新统计表时发生错误: {e}")
            return False

    def _update_customer_statistics(self, temp_df: pd.DataFrame) -> bool:
        """
        更新客户统计表
        空值处理逻辑：客户名称、手机号、客户归属企业的None、空字符串、NaN统一为''，分组和查找时一视同仁。
        """
        try:
            self.logger.info("开始更新客户统计表")
            raw_data_path = self.config.get('data_paths.raw_data')
            customer_stats_file = Path(raw_data_path) / self.config.get('table_files.customer_stats_table')
            main_detail_file = Path(raw_data_path) / self.config.get('table_files.main_detail_table')
            dtype_dict = {
                '客户手机号': str,
                '客户名称': str,
                '客户归属企业': str,
                '申请发放机构': str,
                '活动方案全称': str,
                '归属条线': str,
                '备注（非必填）': str
            }
            # 读取现有客户统计表
            if customer_stats_file.exists():
                customer_stats_df = pd.read_csv(customer_stats_file, encoding='utf-8-sig', dtype=dtype_dict)
            else:
                customer_stats_df = pd.DataFrame()
            # 读取主明细表（含新数据）
            main_detail_df = pd.read_csv(main_detail_file, encoding='utf-8-sig', dtype=dtype_dict)
            # 统一空值处理
            for col in ['客户名称', '客户手机号', '客户归属企业']:
                # replace指定了None和np.nan都替换为''，fillna将空值替换为''
                main_detail_df[col] = main_detail_df[col].replace({None: '', np.nan: ''}).fillna('')
                # 如果客户统计表存在，则更新客户统计表
                if not customer_stats_df.empty and col in customer_stats_df.columns:
                    # 将客户统计表中的空值替换为''
                    customer_stats_df[col] = customer_stats_df[col].replace({None: '', np.nan: ''}).fillna('')
            customer_groups = main_detail_df.groupby(['客户名称', '客户手机号', '客户归属企业'], dropna=False)
            updated_customers = 0
            new_customers = 0
            for (name, phone, enterprise), group in customer_groups:
                total_count = len(group)
                total_amount = group['等值金豆发放数量'].sum()
                institutions = group['申请发放机构'].dropna().unique()
                institutions_str = ','.join(sorted([str(inst) for inst in institutions]))
                # 查找现有记录，空值与''等价，如果客户统计表存在，则更新客户统计表
                if not customer_stats_df.empty:
                    # customer_mask值为True的行是满足条件的行，即客户名称、手机号、客户归属企业都相同
                    customer_mask = (
                        (customer_stats_df['客户名称'] == name) &
                        (customer_stats_df['客户手机号'] == phone) &
                        (customer_stats_df['客户归属企业'] == enterprise)
                    )
                else:
                    # 否则，将customer_mask设置为False
                    customer_mask = pd.Series([False])
                if customer_mask.any():
                    idx = customer_stats_df[customer_mask].index[0]
                    customer_stats_df.loc[idx, '等值金豆发放总次数'] = total_count
                    customer_stats_df.loc[idx, '等值金豆发放户累计'] = total_amount
                    existing_institutions = customer_stats_df.loc[idx, '申请发放机构']
                    if pd.isna(existing_institutions) or existing_institutions == '':
                        customer_stats_df.loc[idx, '申请发放机构'] = institutions_str
                    else:
                        existing_set = set(existing_institutions.split(','))
                        new_set = set(institutions)
                        combined_institutions = ','.join(sorted(existing_set.union(new_set)))
                        customer_stats_df.loc[idx, '申请发放机构'] = combined_institutions
                    updated_customers += 1
                else:
                    new_record = {
                        '记录ID': len(customer_stats_df) + 1,
                        '客户名称': name,
                        '客户手机号': phone,
                        '客户归属企业': enterprise,
                        '等值金豆发放户累计': total_amount,
                        '等值金豆发放总次数': total_count,
                        '申请发放机构': institutions_str,
                        '单次发放超规定次数': 0,
                        '户累计超规定次数': 0,
                        '备注': ''
                    }
                    customer_stats_df = pd.concat([customer_stats_df, pd.DataFrame([new_record])], ignore_index=True)
                    new_customers += 1
            customer_stats_df.to_csv(customer_stats_file, index=False, encoding='utf-8-sig')
            self.result.new_customers = new_customers
            self.result.updated_customers = updated_customers
            self.logger.info(f"客户统计表更新完成: 新增 {new_customers} 个客户, 更新 {updated_customers} 个客户")
            return True
        except Exception as e:
            self.logger.error(f"更新客户统计表时发生错误: {e}")
            return False

    def _update_yearly_statistics(self, temp_df: pd.DataFrame) -> bool:
        """
        更新年度统计表
        空值处理逻辑：客户名称、手机号、客户归属企业的None、空字符串、NaN统一为''，分组和查找时一视同仁。
        """
        try:
            self.logger.info("开始更新年度统计表")
            raw_data_path = self.config.get('data_paths.raw_data')
            yearly_stats_file = Path(raw_data_path) / self.config.get('table_files.yearly_stats_table')
            main_detail_file = Path(raw_data_path) / self.config.get('table_files.main_detail_table')
            if yearly_stats_file.exists():
                yearly_stats_df = pd.read_csv(yearly_stats_file, encoding='utf-8-sig')
            else:
                yearly_stats_df = pd.DataFrame()
            main_detail_df = pd.read_csv(main_detail_file, encoding='utf-8-sig')
            # 统一空值处理
            for col in ['客户名称', '客户手机号', '客户归属企业']:
                main_detail_df[col] = main_detail_df[col].replace({None: '', np.nan: ''}).fillna('')
                if not yearly_stats_df.empty and col in yearly_stats_df.columns:
                    yearly_stats_df[col] = yearly_stats_df[col].replace({None: '', np.nan: ''}).fillna('')
            main_detail_df['年份'] = pd.to_datetime(main_detail_df['申请发放日期']).dt.year
            # 按客户名称、手机号、客户归属企业、年份分组，dropna=False表示不丢弃空值
            yearly_groups = main_detail_df.groupby(['客户名称', '客户手机号', '客户归属企业', '年份'], dropna=False)
            updated_yearly_records = 0
            new_yearly_records = 0
            for (name, phone, enterprise, year), group in yearly_groups:
                total_count = len(group)
                total_amount = group['等值金豆发放数量'].sum()
                institutions = group['申请发放机构'].dropna().unique()
                institutions_str = ','.join(sorted([str(inst) for inst in institutions]))
                if not yearly_stats_df.empty:
                    yearly_mask = (
                        (yearly_stats_df['客户名称'] == name) &
                        (yearly_stats_df['客户手机号'] == phone) &
                        (yearly_stats_df['客户归属企业'] == enterprise) &
                        (yearly_stats_df['发放年份'] == year)
                    )
                else:
                    yearly_mask = pd.Series([False])
                if yearly_mask.any():
                    idx = yearly_stats_df[yearly_mask].index[0]
                    yearly_stats_df.loc[idx, '等值金豆发放总次数'] = total_count
                    yearly_stats_df.loc[idx, '等值金豆发放年累计'] = total_amount
                    existing_institutions = yearly_stats_df.loc[idx, '申请发放机构']
                    if pd.isna(existing_institutions) or existing_institutions == '':
                        yearly_stats_df.loc[idx, '申请发放机构'] = institutions_str
                    else:
                        existing_set = set(existing_institutions.split(','))
                        new_set = set(institutions)
                        combined_institutions = ','.join(sorted(existing_set.union(new_set)))
                        yearly_stats_df.loc[idx, '申请发放机构'] = combined_institutions
                    updated_yearly_records += 1
                else:
                    new_record = {
                        '记录ID': len(yearly_stats_df) + 1,
                        '客户名称': name,
                        '客户手机号': phone,
                        '客户归属企业': enterprise,
                        '申请发放机构': institutions_str,
                        '发放年份': year,
                        '等值金豆发放总次数': total_count,
                        '等值金豆发放年累计': total_amount,
                        '年累计超规定次数': 0,
                        '备注': ''
                    }
                    yearly_stats_df = pd.concat([yearly_stats_df, pd.DataFrame([new_record])], ignore_index=True)
                    new_yearly_records += 1
            yearly_stats_df.to_csv(yearly_stats_file, index=False, encoding='utf-8-sig')
            self.logger.info(f"年度统计表更新完成: 新增 {new_yearly_records} 条记录, 更新 {updated_yearly_records} 条记录")
            return True
        except Exception as e:
            self.logger.error(f"更新年度统计表时发生错误: {e}")
            return False

    def generate_processing_report(self) -> Dict[str, Any]:
        """
        生成处理报告

        Returns:
            处理报告字典
        """
        try:
            report = {
                '处理摘要': self.result.to_dict(),
                '性能数据': self.monitor.get_performance_report(),
                '建议': self._get_processing_recommendations()
            }

            return report

        except Exception as e:
            self.logger.error(f"生成处理报告时发生错误: {e}")
            return {}

    def _get_processing_recommendations(self) -> List[str]:
        """
        获取处理建议

        Returns:
            建议列表
        """
        recommendations = []

        if self.result.success:
            recommendations.append("数据处理成功完成，所有记录已正确更新到主明细表和统计表")

            if self.result.new_customers > 0:
                recommendations.append(f"新增了 {self.result.new_customers} 个客户，建议关注新客户的后续发放情况")

            if self.result.updated_customers > 0:
                recommendations.append(f"更新了 {self.result.updated_customers} 个现有客户的统计数据")

            if self.result.backup_files:
                recommendations.append(f"已创建 {len(self.result.backup_files)} 个备份文件，可用于数据恢复")
        else:
            recommendations.append("数据处理失败，请检查错误信息并重新处理")

            if self.result.backup_files:
                recommendations.append("如需回滚，可使用备份文件恢复数据")

        if self.result.warnings:
            recommendations.append("处理过程中有警告信息，建议检查相关问题")

        return recommendations


# TODO: 扩展点 - 数据处理功能扩展
# 1. 分布式处理支持
# 2. 增量更新优化
# 3. 数据一致性检查
# 4. 自动数据修复功能
# 5. 处理进度实时监控

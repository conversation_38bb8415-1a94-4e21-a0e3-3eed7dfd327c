"""
金豆发放申请校验系统 - 重复和冲突检测模块
负责检测数据重复和客户信息冲突

本模块是数据质量控制的核心组件，主要功能包括：

核心功能：
1. 重复数据检测：基于多字段组合检测完全重复的申请记录
2. 客户信息冲突检测：识别同一客户的信息不一致问题
3. 高效算法实现：使用哈希算法提高大数据量检测效率
4. 详细报告生成：生成包含完整上下文的检测报告

检测类型：
- 重复检测：基于6字段组合（客户名称+手机号+日期+企业+机构+金额）
- 名称冲突：相同手机号+相同企业+不同客户名称
- 手机号冲突：相同客户名称+相同企业+不同手机号

算法特点：
- 哈希优化：使用MD5哈希提高重复检测效率
- 分组算法：按关键字段分组检测冲突
- 内存优化：大数据量时采用分块处理
- 空值处理：统一处理None、空字符串、NaN等空值情况

设计原则：
- 性能优先：针对大数据量优化算法效率
- 准确性保证：严格的冲突检测逻辑
- 可扩展性：易于添加新的检测规则
- 详细记录：完整的问题上下文信息

"""

# 导入标准库
import pandas as pd  # 数据处理核心库
import numpy as np   # 数值计算库，用于空值处理
from typing import Dict, List, Tuple, Set, Any  # 类型注解
from pathlib import Path  # 路径操作
import logging      # 日志记录
from collections import defaultdict  # 默认字典，用于分组统计
import hashlib      # 哈希算法，用于高效重复检测

# 导入自定义工具类
from utils import ConfigManager, LoggerSetup, PerformanceMonitor


class DuplicateRecord:
    """重复记录信息"""
    
    def __init__(self, key_values: Dict[str, Any], records: List[Dict[str, Any]]):
        """
        初始化重复记录信息
        
        Args:
            key_values: 重复判定的关键字段值
            records: 重复的记录列表
        """
        self.key_values = key_values
        self.records = records
        self.count = len(records)
        self.record_ids = [record.get('记录ID', 'N/A') for record in records]
        self.source_files = list(set([record.get('源文件名', 'N/A') for record in records]))
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            '重复判定字段': str(self.key_values),
            '重复次数': self.count,
            '记录ID列表': ', '.join(map(str, self.record_ids)),
            '涉及文件': ', '.join(map(str, self.source_files)),
            '详细记录': self.records
        }


class ConflictRecord:
    """冲突记录信息"""
    
    def __init__(self, conflict_type: str, customer_key: Dict[str, Any], 
                 conflicting_records: List[Dict[str, Any]]):
        """
        初始化冲突记录信息
        
        Args:
            conflict_type: 冲突类型
            customer_key: 客户关键字段
            conflicting_records: 冲突的记录列表
        """
        self.conflict_type = conflict_type
        self.customer_key = customer_key
        self.conflicting_records = conflicting_records
        self.count = len(conflicting_records)
        self.record_ids = [record.get('记录ID', 'N/A') for record in conflicting_records]
        self.source_files = list(set([record.get('源文件名', 'N/A') for record in conflicting_records]))
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            '冲突类型': self.conflict_type,
            '客户关键信息': str(self.customer_key),
            '冲突记录数': self.count,
            '记录ID列表': ', '.join(map(str, self.record_ids)),
            '涉及文件': ', '.join(map(str, self.source_files)),
            '详细记录': self.conflicting_records
        }


class DuplicateConflictDetector:
    """重复和冲突检测器"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化重复和冲突检测器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager
        self.logger = LoggerSetup.setup_logger('DuplicateConflictDetector', 
                                             config_manager.get('logging', {}))
        self.monitor = PerformanceMonitor(config_manager)
    
    def detect_duplicates_and_conflicts(self, temp_df: pd.DataFrame, 
                                      main_df: pd.DataFrame = None) -> Tuple[List[DuplicateRecord], 
                                                                           List[ConflictRecord]]:
        """
        检测重复数据和冲突
        
        这是检测模块的主入口方法，负责协调整个检测流程：
        1. 数据合并和来源标识
        2. 重复数据检测
        3. 客户信息冲突检测
        4. 结果汇总和报告
        
        检测策略：
        - 如果提供主表数据，则检测新数据与历史数据的重复和冲突
        - 如果只有临时表数据，则检测临时表内部的重复和冲突
        - 为所有数据添加来源标识，便于追踪问题来源
        
        Args:
            temp_df: 临时汇总表数据（新输入的数据）
            main_df: 主明细表数据（历史数据，可选）
            
        Returns:
            Tuple[List[DuplicateRecord], List[ConflictRecord]]: 
                - 重复记录列表：完全重复的数据组
                - 冲突记录列表：客户信息不一致的数据组
                
        处理逻辑：
        1. 数据预处理：添加来源标识，合并数据集
        2. 重复检测：基于关键字段组合检测完全重复
        3. 冲突检测：检测客户信息的不一致性
        4. 性能监控：记录各阶段的处理时间
        """
        try:
            # 开始性能监控
            self.monitor.start_monitoring("重复和冲突检测")
            
            # ========== 数据合并和来源标识 ==========
            # 根据是否有主表数据决定检测策略
            if main_df is not None and not main_df.empty:
                # 策略1：检测新数据与历史数据的重复和冲突
                
                # 为临时表数据添加来源标识
                temp_df_with_source = temp_df.copy()
                temp_df_with_source['数据来源'] = '新输入数据'  # 标记为新数据
                # 设置源文件名，如果已有则保持，否则使用默认名称
                temp_df_with_source['源文件名'] = temp_df_with_source.get('源文件名', '金豆发放申请明细表.csv')
                
                # 为主表数据添加来源标识
                main_df_with_source = main_df.copy()
                main_df_with_source['数据来源'] = '历史数据'  # 标记为历史数据
                main_df_with_source['源文件名'] = '金豆发放申请主明细表.csv'  # 主表固定文件名
                
                # 合并新数据和历史数据
                combined_df = pd.concat([temp_df_with_source, main_df_with_source], ignore_index=True)
                self.logger.info(f"合并临时表({len(temp_df)}条)和主表({len(main_df)}条)进行检测，已添加数据来源标识")
            else:
                # 策略2：仅检测临时表内部的重复和冲突
                combined_df = temp_df.copy()
                combined_df['数据来源'] = '新输入数据'  # 全部标记为新数据
                # 设置源文件名，如果已有则保持，否则使用默认名称
                combined_df['源文件名'] = combined_df.get('源文件名', '金豆发放申请明细表.csv')
                self.logger.info(f"仅对临时表({len(temp_df)}条)进行检测，已添加数据来源标识")
            
            # 记录数据合并完成的性能检查点
            self.monitor.checkpoint("数据合并完成")
            
            # ========== 重复数据检测 ==========
            # 使用高效算法检测完全重复的记录
            duplicate_records = self._detect_duplicates(combined_df)
            self.monitor.checkpoint("重复检测完成")
            
            # ========== 客户信息冲突检测 ==========
            # 检测同一客户的信息不一致问题
            conflict_records = self._detect_conflicts(combined_df)
            self.monitor.checkpoint("冲突检测完成")
            
            # 结束性能监控
            self.monitor.end_monitoring("重复和冲突检测")
            
            # 记录检测结果摘要
            self.logger.info(f"检测完成: 发现 {len(duplicate_records)} 组重复数据, "
                           f"{len(conflict_records)} 组冲突数据")
            
            return duplicate_records, conflict_records
            
        except Exception as e:
            # 捕获并记录检测过程中的异常
            self.logger.error(f"检测重复和冲突时发生错误: {e}")
            raise  # 重新抛出异常，让调用者处理
    
    def _detect_duplicates(self, df: pd.DataFrame) -> List[DuplicateRecord]:
        """
        检测重复数据
        
        Args:
            df: 合并临时表和主表后的数据框
            
        Returns:
            重复记录列表
        """
        try:
            duplicate_records = []
            duplicate_key_fields = self.config.get('duplicate_detection.duplicate_key_fields', [])
            
            # 过滤存在的字段
            existing_fields = [field for field in duplicate_key_fields if field in df.columns]
            
            if not existing_fields:
                self.logger.warning("没有找到重复检测所需的字段")
                return duplicate_records
            
            self.logger.info(f"使用字段进行重复检测: {existing_fields}")
            
            df_with_key = df.copy()
            # df_with_key['_duplicate_key'] 最终会成为一个新的列，其中的值是根据 existing_fields 列中的数据生成的哈希键（或唯一标识字符串），用于标记哪些行具有重复的数据组合。
            # 当多行会有相同的 existing_fields 列数据时，它们会生成相同的 _duplicate_key 值，这样就可以通过 groupby('_duplicate_key') 快速找到重复的行。
            df_with_key['_duplicate_key'] = df_with_key[existing_fields].apply(
                # row.to_dict(): 将当前行的数据转换为字典格式，键是列名，值是该列对应的数据。axis=1: 表示按行
                lambda row: self._create_hash_key(row.to_dict()), axis=1
            )
            
            # 按复合键分组
            grouped = df_with_key.groupby('_duplicate_key')
            
            for key, group in grouped:
                if len(group) > 1:
                    # 从分组后的重复数据中提取关键信息，构建一个包含重复记录详情的对象，并将其添加到列表中。
                    key_values = group.iloc[0][existing_fields].to_dict()
                    records = group.drop('_duplicate_key', axis=1).to_dict('records')   # 移除分组用的 _duplicate_key 列
                    
                    duplicate_record = DuplicateRecord(key_values, records)
                    duplicate_records.append(duplicate_record)
                    
                    self.logger.debug(f"发现重复数据: {key_values}, 重复次数: {len(group)}")
            
            return duplicate_records
            
        except Exception as e:
            self.logger.error(f"检测重复数据时发生错误: {e}")
            return []
    
    def _detect_conflicts(self, df: pd.DataFrame) -> List[ConflictRecord]:
        """
        检测客户信息冲突
        
        Args:
            df: 合并临时表和主表后的数据框
            
        Returns:
            冲突记录列表
        """
        try:
            conflict_records = []
            customer_key_fields = self.config.get('duplicate_detection.conflict_detection.customer_key_fields', [])
            
            # 过滤存在的字段
            existing_fields = [field for field in customer_key_fields if field in df.columns]
            
            if len(existing_fields) < 3:  # 需要客户名称、手机号、归属企业
                self.logger.warning("客户信息冲突检测所需字段不完整")
                return conflict_records
            
            self.logger.info(f"使用字段进行冲突检测: {existing_fields}")
            
            # 按不同的组合检测冲突
            conflicts = []
            
            # 冲突类型1: 相同手机号+相同客户归属企业+不同客户名称
            conflicts.extend(self._detect_name_conflicts(df))
            
            # 冲突类型2: 相同客户名称+相同客户归属企业+不同手机号
            conflicts.extend(self._detect_phone_conflicts(df))
            
            return conflicts
            
        except Exception as e:
            self.logger.error(f"检测客户信息冲突时发生错误: {e}")
            return []
    
    def _detect_name_conflicts(self, df: pd.DataFrame) -> List[ConflictRecord]:
        """
        检测客户名称冲突（冲突类型A：相同手机号+相同客户归属企业+不同客户名称）
        空值处理逻辑：将客户归属企业的None、空字符串、NaN统一为''，分组和比较时一视同仁。
        """
        try:
            name_conflicts = []
            # 统一空值处理
            df = df.copy()
            df['客户归属企业'] = df['客户归属企业'].replace({None: '', np.nan: ''}).fillna('')
            df['客户手机号'] = df['客户手机号'].replace({None: '', np.nan: ''}).fillna('')
            df['客户名称'] = df['客户名称'].replace({None: '', np.nan: ''}).fillna('')
            # 按“客户手机号”和“客户归属企业”两列分组，dropna=False表示：即使这两列有空值，也保留分组（不丢弃）
            grouped = df.groupby(['客户手机号', '客户归属企业'], dropna=False)
            for (phone, enterprise), group in grouped:
                # 先判断当前分组是否需要检查冲突
                if not self._should_check_conflict(phone, enterprise, group):
                    continue   # 不需要检查则跳过
                # 提取组内所有非空的“客户名称”，并去重
                unique_names = group['客户名称'].dropna().unique()
                # 如果去重后的名称数量>1，说明存在冲突（同一手机号+企业对应多个名称）
                if len(unique_names) > 1:
                    # 构建冲突记录
                    customer_key = {
                        '客户手机号': phone,
                        '客户归属企业': enterprise
                    }
                    records = group.to_dict('records')
                    conflict = ConflictRecord(
                        conflict_type="相同手机号+相同客户归属企业+不同客户名称",
                        customer_key=customer_key,
                        conflicting_records=records
                    )
                    name_conflicts.append(conflict)
                    self.logger.debug(f"发现名称冲突: 手机号={phone}, 企业={enterprise}, 不同名称={list(unique_names)}")
            return name_conflicts
        except Exception as e:
            self.logger.error(f"检测客户名称冲突时发生错误: {e}")
            return []
    
    def _detect_phone_conflicts(self, df: pd.DataFrame) -> List[ConflictRecord]:
        """
        检测手机号冲突（冲突类型B：相同客户名称+相同客户归属企业+不同手机号）
        空值处理逻辑：将客户归属企业的None、空字符串、NaN统一为''，分组和比较时一视同仁。
        """
        try:
            phone_conflicts = []
            # 统一空值处理
            df = df.copy()
            df['客户归属企业'] = df['客户归属企业'].replace({None: '', np.nan: ''}).fillna('')
            df['客户手机号'] = df['客户手机号'].replace({None: '', np.nan: ''}).fillna('')
            df['客户名称'] = df['客户名称'].replace({None: '', np.nan: ''}).fillna('')
            # 按客户名称和客户归属企业分组
            grouped = df.groupby(['客户名称', '客户归属企业'], dropna=False)
            for (name, enterprise), group in grouped:
                if not self._should_check_conflict(name, enterprise, group):
                    continue
                unique_phones = group['客户手机号'].dropna().unique()
                if len(unique_phones) > 1:
                    customer_key = {
                        '客户名称': name,
                        '客户归属企业': enterprise
                    }
                    records = group.to_dict('records')
                    conflict = ConflictRecord(
                        conflict_type="相同客户名称+相同客户归属企业+不同手机号",
                        customer_key=customer_key,
                        conflicting_records=records
                    )
                    phone_conflicts.append(conflict)
                    self.logger.debug(f"发现手机号冲突: 名称={name}, 企业={enterprise}, 不同手机号={list(unique_phones)}")
            return phone_conflicts
        except Exception as e:
            self.logger.error(f"检测手机号冲突时发生错误: {e}")
            return []

    # 对 “组内是否有非空企业值” 的检查，本质是：
    # 正常情况：分组键为 '' 时，组内必然全是 ''（预处理保证），此时可以安全检查名称冲突。
    # 异常情况：如果组内出现非空企业值，说明分组逻辑出错，此时放弃检查，避免错误结果。
    def _should_check_conflict(self, key1: Any, key2: Any, group: pd.DataFrame) -> bool:
        """
        判断是否应该检测冲突
        空值处理逻辑：客户归属企业只要全为空（None、''、NaN都视为''），则允许检测；否则只检测企业完全一致的分组。
        """
        try:
            # key1是“客户手机号”/“客户名称”：如果手机号/客户名称为空，则不需要检查
            if pd.isna(key1) or key1 == '':
                return False
            # key2是“客户归属企业”：如果企业为空，把 None 和 np.nan 都替换为空字符串，确保所有空值都被填充为空字符串
            if pd.isna(key2) or key2 == '':
                # 检查组内所有“客户归属企业”是否有非空值
                enterprise_values = group['客户归属企业'].replace({None: '', np.nan: ''}).fillna('')
                # 提取这一组中所有不重复的企业值
                unique_enterprises = enterprise_values.unique()   
                # 只要组内有非空企业，则不检测冲突
                if any(e != '' for e in unique_enterprises):
                    return False
            return True
        except Exception as e:
            self.logger.warning(f"判断冲突检测条件时发生错误: {e}")
            return False

    def _create_hash_key(self, record: Dict[str, Any]) -> str:
        """
        创建记录的哈希键，用于高效的重复检测

        Args:
            record: 记录字典

        Returns:
            哈希键字符串
        """
        try:
            # 将记录值转换为字符串并排序，确保一致性
            key_parts = []
            for key in sorted(record.keys()):
                value = record[key]
                if pd.isna(value):
                    key_parts.append(f"{key}:NULL")
                else:
                    key_parts.append(f"{key}:{str(value)}")

            key_string = "|".join(key_parts)
            return hashlib.md5(key_string.encode('utf-8')).hexdigest()

        except Exception as e:
            self.logger.warning(f"创建哈希键时发生错误: {e}")
            return str(hash(str(record)))

    def generate_duplicate_report(self, duplicate_records: List[DuplicateRecord]) -> pd.DataFrame:
        """
        生成重复数据报告

        Args:
            duplicate_records: 重复记录列表

        Returns:
            重复数据报告数据框
        """
        try:
            if not duplicate_records:
                self.logger.info("没有发现重复数据")
                return pd.DataFrame()

            # 转换为数据框
            report_data = []
            for i, duplicate in enumerate(duplicate_records, 1):
                base_info = duplicate.to_dict()
                base_info['重复组编号'] = i

                # 为每个重复记录创建一行
                for j, record in enumerate(duplicate.records):
                    row = base_info.copy()
                    row['记录序号'] = j + 1
                    # 确保所有值都转换为字符串，避免类型错误
                    processed_record = {}   
                    for key, value in record.items():
                        if pd.isna(value):
                            processed_record[key] = ''
                        else:
                            processed_record[key] = str(value)
                    
                    # 确保数据来源信息正确显示
                    if '数据来源' not in processed_record:
                        processed_record['数据来源'] = '未知'
                    if '源文件名' not in processed_record:
                        processed_record['源文件名'] = '未知文件'
                    
                    row.update(processed_record)
                    report_data.append(row)

            report_df = pd.DataFrame(report_data)

            self.logger.info(f"重复数据报告生成完成，共 {len(duplicate_records)} 组重复数据，"
                           f"涉及 {len(report_data)} 条记录")

            return report_df

        except Exception as e:
            self.logger.error(f"生成重复数据报告时发生错误: {e}")
            return pd.DataFrame()

    def generate_conflict_report(self, conflict_records: List[ConflictRecord]) -> pd.DataFrame:
        """
        生成冲突数据报告

        Args:
            conflict_records: 冲突记录列表

        Returns:
            冲突数据报告数据框
        """
        try:
            if not conflict_records:
                self.logger.info("没有发现冲突数据")
                return pd.DataFrame()

            # 转换为数据框
            report_data = []
            for i, conflict in enumerate(conflict_records, 1):
                base_info = conflict.to_dict()
                base_info['冲突组编号'] = i

                # 为每个冲突记录创建一行
                for j, record in enumerate(conflict.conflicting_records):
                    row = base_info.copy()
                    row['记录序号'] = j + 1
                    # 确保所有值都转换为字符串，避免类型错误
                    processed_record = {}
                    for key, value in record.items():
                        if pd.isna(value):
                            processed_record[key] = ''
                        else:
                            processed_record[key] = str(value)
                    
                    # 确保数据来源信息正确显示
                    if '数据来源' not in processed_record:
                        processed_record['数据来源'] = '未知'
                    if '源文件名' not in processed_record:
                        processed_record['源文件名'] = '未知文件'
                    
                    row.update(processed_record)
                    report_data.append(row)

            report_df = pd.DataFrame(report_data)

            self.logger.info(f"冲突数据报告生成完成，共 {len(conflict_records)} 组冲突数据，"
                           f"涉及 {len(report_data)} 条记录")

            return report_df

        except Exception as e:
            self.logger.error(f"生成冲突数据报告时发生错误: {e}")
            return pd.DataFrame()

    def get_detection_summary(self, duplicate_records: List[DuplicateRecord],
                            conflict_records: List[ConflictRecord]) -> Dict[str, Any]:
        """
        获取检测结果摘要

        Args:
            duplicate_records: 重复记录列表
            conflict_records: 冲突记录列表

        Returns:
            检测结果摘要字典
        """
        try:
            # 统计重复数据
            total_duplicate_groups = len(duplicate_records)
            total_duplicate_records = sum(dup.count for dup in duplicate_records)

            # 统计冲突数据
            total_conflict_groups = len(conflict_records)
            total_conflict_records = sum(conf.count for conf in conflict_records)

            # 按冲突类型统计
            conflict_type_stats = defaultdict(int)
            for conflict in conflict_records:
                conflict_type_stats[conflict.conflict_type] += 1

            summary = {
                '重复数据统计': {
                    '重复组数': total_duplicate_groups,
                    '重复记录总数': total_duplicate_records
                },
                '冲突数据统计': {
                    '冲突组数': total_conflict_groups,
                    '冲突记录总数': total_conflict_records,
                    '按类型统计': dict(conflict_type_stats)
                },
                '检测结果': {
                    '存在重复': total_duplicate_groups > 0,
                    '存在冲突': total_conflict_groups > 0,
                    '建议操作': self._get_recommended_action(total_duplicate_groups, total_conflict_groups)
                }
            }

            return summary

        except Exception as e:
            self.logger.error(f"生成检测摘要时发生错误: {e}")
            return {}

    def _get_recommended_action(self, duplicate_count: int, conflict_count: int) -> str:
        """
        根据检测结果获取建议操作

        Args:
            duplicate_count: 重复组数
            conflict_count: 冲突组数

        Returns:
            建议操作描述
        """
        if duplicate_count > 0 and conflict_count > 0:
            return "存在重复和冲突数据，建议先解决重复问题，再处理冲突问题"
        elif duplicate_count > 0:
            return "存在重复数据，必须解决后才能继续处理"
        elif conflict_count > 0:
            return "仅存在冲突数据，可选择继续处理或先解决冲突"
        else:
            return "数据检测通过，可以继续后续处理"


# TODO: 扩展点 - 重复和冲突检测功能扩展
# 1. 模糊匹配检测（相似客户名称、手机号）
# 2. 智能冲突解决建议
# 3. 历史冲突记录跟踪
# 4. 批量冲突处理工具
# 5. 冲突检测规则自定义配置
